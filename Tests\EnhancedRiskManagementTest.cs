using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;
using ZeroDateStrat.Utils;

namespace ZeroDateStrat.Tests;

public class EnhancedRiskManagementTest
{
    private readonly ILogger<EnhancedRiskManagementTest> _logger;
    private readonly IServiceProvider _serviceProvider;

    public EnhancedRiskManagementTest()
    {
        // Setup dependency injection for testing
        var services = new ServiceCollection();
        
        // Add logging with debug level
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Debug);
        });
        
        // Add configuration
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: false)
            .Build();
        services.AddSingleton<IConfiguration>(configuration);
        
        // Add services
        services.AddScoped<IAlpacaService, AlpacaService>();
        services.AddScoped<IRiskManager, RiskManager>();
        services.AddScoped<IMarketRegimeAnalyzer, MarketRegimeAnalyzer>();
        
        _serviceProvider = services.BuildServiceProvider();
        _logger = _serviceProvider.GetRequiredService<ILogger<EnhancedRiskManagementTest>>();
    }

    public async Task TestEnhancedRiskManagement()
    {
        Console.WriteLine("=== Enhanced Risk Management Test ===\n");

        try
        {
            var riskManager = _serviceProvider.GetRequiredService<IRiskManager>();
            var marketRegimeAnalyzer = _serviceProvider.GetRequiredService<IMarketRegimeAnalyzer>();

            // Test 1: Portfolio Risk Metrics
            Console.WriteLine("1. Testing Portfolio Risk Metrics...");
            var portfolioMetrics = await riskManager.GetPortfolioRiskMetricsAsync();
            
            Console.WriteLine($"   Active Positions: {portfolioMetrics.ActivePositions}");
            Console.WriteLine($"   Total Exposure: {portfolioMetrics.TotalExposure:C2}");
            Console.WriteLine($"   Net Delta: {portfolioMetrics.NetDelta:F2}");
            Console.WriteLine($"   Net Gamma: {portfolioMetrics.NetGamma:F4}");
            Console.WriteLine($"   Net Theta: {portfolioMetrics.NetTheta:F2}");
            Console.WriteLine($"   Net Vega: {portfolioMetrics.NetVega:F2}");
            Console.WriteLine($"   Portfolio VaR: {portfolioMetrics.PortfolioVaR:C2}");
            Console.WriteLine($"   Concentration Risk: {portfolioMetrics.ConcentrationRisk:P2}");
            
            if (portfolioMetrics.SymbolExposure.Any())
            {
                Console.WriteLine("   Symbol Exposure:");
                foreach (var exposure in portfolioMetrics.SymbolExposure)
                {
                    Console.WriteLine($"     {exposure.Key}: {exposure.Value:C2}");
                }
            }

            // Test 2: Market Regime Analysis
            Console.WriteLine("\n2. Testing Market Regime Analysis...");
            var currentRegime = await marketRegimeAnalyzer.GetCurrentRegimeAsync();
            
            Console.WriteLine($"   VIX Level: {currentRegime.Vix:F2}");
            Console.WriteLine($"   Volatility Regime: {currentRegime.VolatilityRegime}");
            Console.WriteLine($"   Market Trend: {currentRegime.Trend}");
            Console.WriteLine($"   Confidence: {currentRegime.Confidence:P1}");
            Console.WriteLine($"   Description: {currentRegime.Description}");

            // Test 3: Risk Adjustment Based on Market Regime
            Console.WriteLine("\n3. Testing Risk Adjustment...");
            var riskAdjustment = await riskManager.GetRiskAdjustmentAsync(currentRegime);
            
            Console.WriteLine($"   Position Size Multiplier: {riskAdjustment.PositionSizeMultiplier:F2}");
            Console.WriteLine($"   Confidence Threshold: {riskAdjustment.ConfidenceThreshold:P1}");
            Console.WriteLine($"   Max Risk Per Trade: {riskAdjustment.MaxRiskPerTrade:C2}");
            Console.WriteLine($"   Allow New Positions: {riskAdjustment.AllowNewPositions}");
            Console.WriteLine($"   Reason: {riskAdjustment.Reason}");

            // Test 4: Create Test Signal and Validate
            Console.WriteLine("\n4. Testing Signal Validation...");
            var testSignal = CreateTestSignal();
            
            Console.WriteLine($"   Test Signal: {testSignal.Strategy} for {testSignal.UnderlyingSymbol}");
            Console.WriteLine($"   Expected Profit: {testSignal.ExpectedProfit:C2}");
            Console.WriteLine($"   Max Loss: {testSignal.MaxLoss:C2}");
            Console.WriteLine($"   Confidence: {testSignal.Confidence:P1}");
            Console.WriteLine($"   Risk/Reward: {testSignal.RiskRewardRatio:F2}");

            // Test basic validation
            var isValid = await riskManager.ValidateSignalAsync(testSignal);
            Console.WriteLine($"   Signal Valid: {isValid}");

            // Test concentration risk
            var concentrationOk = await riskManager.ValidatePortfolioConcentrationAsync(testSignal);
            Console.WriteLine($"   Concentration OK: {concentrationOk}");

            // Test correlation risk
            var correlationOk = await riskManager.CheckCorrelationRiskAsync(testSignal);
            Console.WriteLine($"   Correlation OK: {correlationOk}");

            // Test drawdown limits
            var drawdownOk = await riskManager.CheckDrawdownLimitsAsync();
            Console.WriteLine($"   Drawdown OK: {drawdownOk}");

            // Test 5: Dynamic Position Sizing
            Console.WriteLine("\n5. Testing Dynamic Position Sizing...");
            var basePositionSize = await riskManager.CalculatePositionSizeAsync(testSignal);
            var dynamicPositionSize = await riskManager.CalculateDynamicPositionSizeAsync(testSignal, currentRegime);
            
            Console.WriteLine($"   Base Position Size: {basePositionSize}");
            Console.WriteLine($"   Dynamic Position Size: {dynamicPositionSize}");
            Console.WriteLine($"   Adjustment Factor: {(dynamicPositionSize / Math.Max(basePositionSize, 1)):F2}");

            // Test 6: VaR Limits
            Console.WriteLine("\n6. Testing VaR Limits...");
            var varLimit = await riskManager.GetVaRLimitAsync();
            Console.WriteLine($"   VaR Limit: {varLimit:C2}");
            Console.WriteLine($"   Current Portfolio VaR: {portfolioMetrics.PortfolioVaR:C2}");
            Console.WriteLine($"   VaR Utilization: {(portfolioMetrics.PortfolioVaR / Math.Max(varLimit, 1)):P1}");

            // Test 7: Account Limits
            Console.WriteLine("\n7. Testing Account Limits...");
            var accountOk = await riskManager.CheckAccountLimitsAsync();
            var maxRiskPerTrade = await riskManager.GetMaxRiskPerTradeAsync();
            var dailyPnL = await riskManager.GetDailyPnLAsync();
            
            Console.WriteLine($"   Account Limits OK: {accountOk}");
            Console.WriteLine($"   Max Risk Per Trade: {maxRiskPerTrade:C2}");
            Console.WriteLine($"   Daily P&L: {dailyPnL:C2}");

            Console.WriteLine("\n=== Enhanced Risk Management Test Completed ===");

        }
        catch (Exception ex)
        {
            Console.WriteLine($"Enhanced risk management test failed: {ex.Message}");
            _logger.LogError(ex, "Enhanced risk management test failed");
        }
    }

    private TradingSignal CreateTestSignal()
    {
        return new TradingSignal
        {
            Id = Guid.NewGuid().ToString(),
            Strategy = "IronButterfly",
            UnderlyingSymbol = "SPY",
            ExpirationDate = DateTime.Today,
            ExpectedProfit = 25m,
            MaxLoss = 475m,
            Confidence = 0.65m,
            GeneratedAt = DateTime.UtcNow,
            Legs = new List<OptionLeg>
            {
                new OptionLeg
                {
                    OptionType = OptionType.Call,
                    Side = OrderSide.Sell,
                    StrikePrice = 455m,
                    Quantity = 1,
                    Price = 0.84m
                },
                new OptionLeg
                {
                    OptionType = OptionType.Put,
                    Side = OrderSide.Sell,
                    StrikePrice = 455m,
                    Quantity = 1,
                    Price = 0.99m
                },
                new OptionLeg
                {
                    OptionType = OptionType.Call,
                    Side = OrderSide.Buy,
                    StrikePrice = 465m,
                    Quantity = 1,
                    Price = 0.75m
                },
                new OptionLeg
                {
                    OptionType = OptionType.Put,
                    Side = OrderSide.Buy,
                    StrikePrice = 445m,
                    Quantity = 1,
                    Price = 0.75m
                }
            }
        };
    }

    public static async Task RunEnhancedRiskManagementTest()
    {
        var test = new EnhancedRiskManagementTest();
        await test.TestEnhancedRiskManagement();
    }
}
