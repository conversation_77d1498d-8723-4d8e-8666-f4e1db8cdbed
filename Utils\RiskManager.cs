using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Utils;

public interface IRiskManager
{
    Task<bool> ValidateSignalAsync(TradingSignal signal);
    Task<decimal> CalculatePositionSizeAsync(TradingSignal signal);
    Task<decimal> GetDailyPnLAsync();
    Task<bool> CheckAccountLimitsAsync();
    Task<decimal> GetMaxRiskPerTradeAsync();

    // Enhanced risk management methods
    Task<PortfolioRiskMetrics> GetPortfolioRiskMetricsAsync();
    Task<bool> CheckCorrelationRiskAsync(TradingSignal signal);
    Task<decimal> CalculateDynamicPositionSizeAsync(TradingSignal signal, MarketRegime regime);
    Task<bool> CheckDrawdownLimitsAsync();
    Task<decimal> GetVaRLimitAsync();
    Task<bool> ValidatePortfolioConcentrationAsync(TradingSignal signal);
    Task<RiskAdjustment> GetRiskAdjustmentAsync(MarketRegime regime);
}

public class PortfolioRiskMetrics
{
    public decimal TotalExposure { get; set; }
    public decimal NetDelta { get; set; }
    public decimal NetGamma { get; set; }
    public decimal NetTheta { get; set; }
    public decimal NetVega { get; set; }
    public decimal PortfolioVaR { get; set; }
    public decimal MaxDrawdown { get; set; }
    public decimal ConcentrationRisk { get; set; }
    public int ActivePositions { get; set; }
    public Dictionary<string, decimal> SymbolExposure { get; set; } = new();
}

public class RiskAdjustment
{
    public decimal PositionSizeMultiplier { get; set; } = 1.0m;
    public decimal ConfidenceThreshold { get; set; } = 0.6m;
    public decimal MaxRiskPerTrade { get; set; }
    public bool AllowNewPositions { get; set; } = true;
    public string Reason { get; set; } = string.Empty;
}

public class RiskManager : IRiskManager
{
    private readonly ILogger<RiskManager> _logger;
    private readonly IConfiguration _configuration;
    private readonly IAlpacaService _alpacaService;

    public RiskManager(
        ILogger<RiskManager> logger,
        IConfiguration configuration,
        IAlpacaService alpacaService)
    {
        _logger = logger;
        _configuration = configuration;
        _alpacaService = alpacaService;
    }

    public async Task<bool> ValidateSignalAsync(TradingSignal signal)
    {
        try
        {
            // Check if signal is valid
            if (!signal.IsValid)
            {
                _logger.LogWarning($"Signal {signal.Id} is not valid");
                return false;
            }

            // Check confidence threshold (dynamic based on market regime)
            var minConfidence = 0.6m;
            if (signal.Confidence < minConfidence)
            {
                _logger.LogWarning($"Signal {signal.Id} confidence {signal.Confidence:P} below threshold {minConfidence:P}");
                return false;
            }

            // Check risk-reward ratio
            var minRiskReward = 0.3m;
            if (signal.RiskRewardRatio < minRiskReward)
            {
                _logger.LogWarning($"Signal {signal.Id} risk-reward ratio {signal.RiskRewardRatio:F2} below threshold {minRiskReward:F2}");
                return false;
            }

            // Enhanced risk checks
            if (!await CheckDrawdownLimitsAsync())
            {
                _logger.LogWarning("Drawdown limits exceeded");
                return false;
            }

            if (!await ValidatePortfolioConcentrationAsync(signal))
            {
                _logger.LogWarning($"Portfolio concentration limits exceeded for {signal.UnderlyingSymbol}");
                return false;
            }

            if (!await CheckCorrelationRiskAsync(signal))
            {
                _logger.LogWarning($"Correlation risk too high for {signal.UnderlyingSymbol}");
                return false;
            }

            // Check maximum loss per trade
            var maxRiskPerTrade = await GetMaxRiskPerTradeAsync();
            if (signal.MaxLoss > maxRiskPerTrade)
            {
                _logger.LogWarning($"Signal {signal.Id} max loss {signal.MaxLoss:C} exceeds per-trade limit {maxRiskPerTrade:C}");
                return false;
            }

            // Check account limits
            if (!await CheckAccountLimitsAsync())
            {
                _logger.LogWarning("Account limits exceeded");
                return false;
            }

            // Check if we already have too many positions in the same underlying
            var maxPositionsPerUnderlying = 3;
            var existingPositions = await GetExistingPositionsForSymbol(signal.UnderlyingSymbol);
            if (existingPositions >= maxPositionsPerUnderlying)
            {
                _logger.LogWarning($"Too many existing positions for {signal.UnderlyingSymbol}: {existingPositions}");
                return false;
            }

            // Check time to expiration (should be 0 for 0 DTE)
            if (signal.ExpirationDate.Date != DateTime.Today)
            {
                _logger.LogWarning($"Signal {signal.Id} is not 0 DTE: expires {signal.ExpirationDate:yyyy-MM-dd}");
                return false;
            }

            // Check market hours
            if (!IsMarketHours())
            {
                _logger.LogWarning("Outside market hours");
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error validating signal {signal.Id}");
            return false;
        }
    }

    public async Task<decimal> CalculatePositionSizeAsync(TradingSignal signal)
    {
        try
        {
            var account = await _alpacaService.GetAccountAsync();
            if (account == null)
                return 0;

            var buyingPower = account.BuyingPower ?? 0;
            var riskPerTrade = _configuration.GetValue<decimal>("Trading:RiskPerTrade", 0.02m);
            var maxPositionSize = _configuration.GetValue<decimal>("Trading:MaxPositionSize", 10000);

            // Calculate position size based on risk per trade
            var accountValue = account.Equity ?? 0;
            var maxRiskAmount = accountValue * riskPerTrade;

            // Position size based on maximum loss
            var positionSizeByRisk = signal.MaxLoss > 0 ? maxRiskAmount / signal.MaxLoss : 0;

            // Position size based on buying power (conservative approach)
            var conservativeBuyingPower = Math.Min(buyingPower * 0.1m, maxPositionSize);
            var positionSizeByBuyingPower = conservativeBuyingPower / (signal.MaxLoss > 0 ? signal.MaxLoss : 1000);

            // Take the smaller of the two
            var positionSize = Math.Min(positionSizeByRisk, positionSizeByBuyingPower);
            
            // Ensure minimum position size of 1
            positionSize = Math.Max(1, Math.Floor(positionSize));

            _logger.LogInformation($"Calculated position size: {positionSize} for signal {signal.Id}");
            return positionSize;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating position size for signal {signal.Id}");
            return 0;
        }
    }

    public async Task<decimal> GetDailyPnLAsync()
    {
        try
        {
            // In a real implementation, you'd calculate this from today's trades
            // For now, return a placeholder
            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting daily P&L");
            return 0;
        }
    }

    public async Task<bool> CheckAccountLimitsAsync()
    {
        try
        {
            var account = await _alpacaService.GetAccountAsync();
            if (account == null)
                return false;

            // Check buying power
            var minBuyingPower = 1000m;
            var buyingPower = account.BuyingPower ?? 0;
            if (buyingPower < minBuyingPower)
            {
                _logger.LogWarning($"Insufficient buying power: {buyingPower:C} < {minBuyingPower:C}");
                return false;
            }

            // Check if account has sufficient equity
            var equity = account.Equity ?? 0;
            if (equity < 25000)
            {
                _logger.LogWarning("Insufficient equity for options trading");
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking account limits");
            return false;
        }
    }

    public async Task<decimal> GetMaxRiskPerTradeAsync()
    {
        try
        {
            var account = await _alpacaService.GetAccountAsync();
            if (account == null)
                return 0;

            var riskPerTrade = _configuration.GetValue<decimal>("Trading:RiskPerTrade", 0.02m);
            return (account.Equity ?? 0) * riskPerTrade;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting max risk per trade");
            return 0;
        }
    }

    private async Task<int> GetExistingPositionsForSymbol(string symbol)
    {
        try
        {
            var positions = await _alpacaService.GetPositionsAsync();
            return positions.Count(p => p.Symbol.StartsWith(symbol));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error getting existing positions for {symbol}");
            return 0;
        }
    }

    private bool IsMarketHours()
    {
        var now = DateTime.Now;
        var tradingStart = TimeSpan.Parse(_configuration["Trading:TradingStartTime"] ?? "09:30:00");
        var tradingEnd = TimeSpan.Parse(_configuration["Trading:TradingEndTime"] ?? "15:30:00");

        return now.DayOfWeek != DayOfWeek.Saturday &&
               now.DayOfWeek != DayOfWeek.Sunday &&
               now.TimeOfDay >= tradingStart &&
               now.TimeOfDay <= tradingEnd;
    }

    // Enhanced Risk Management Methods
    public async Task<PortfolioRiskMetrics> GetPortfolioRiskMetricsAsync()
    {
        try
        {
            var positions = await _alpacaService.GetPositionsAsync();
            var account = await _alpacaService.GetAccountAsync();

            var metrics = new PortfolioRiskMetrics
            {
                ActivePositions = positions.Count,
                TotalExposure = positions.Sum(p => Math.Abs(p.MarketValue ?? 0)),
                NetDelta = CalculateNetDelta(positions),
                NetGamma = CalculateNetGamma(positions),
                NetTheta = CalculateNetTheta(positions),
                NetVega = CalculateNetVega(positions)
            };

            // Calculate symbol exposure
            foreach (var position in positions)
            {
                var symbol = ExtractUnderlyingSymbol(position.Symbol);
                if (!metrics.SymbolExposure.ContainsKey(symbol))
                    metrics.SymbolExposure[symbol] = 0;
                metrics.SymbolExposure[symbol] += Math.Abs(position.MarketValue ?? 0);
            }

            // Calculate concentration risk (max exposure to single symbol / total exposure)
            if (metrics.TotalExposure > 0)
            {
                metrics.ConcentrationRisk = metrics.SymbolExposure.Values.Max() / metrics.TotalExposure;
            }

            // Calculate portfolio VaR (simplified)
            metrics.PortfolioVaR = CalculatePortfolioVaR(positions);

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating portfolio risk metrics");
            return new PortfolioRiskMetrics();
        }
    }

    public async Task<bool> CheckCorrelationRiskAsync(TradingSignal signal)
    {
        try
        {
            var positions = await _alpacaService.GetPositionsAsync();
            var symbolExposure = new Dictionary<string, decimal>();

            // Calculate current exposure by symbol
            foreach (var position in positions)
            {
                var symbol = ExtractUnderlyingSymbol(position.Symbol);
                if (!symbolExposure.ContainsKey(symbol))
                    symbolExposure[symbol] = 0;
                symbolExposure[symbol] += Math.Abs(position.MarketValue ?? 0);
            }

            // Check if adding this signal would create excessive correlation
            var maxCorrelatedExposure = _configuration.GetValue<decimal>("Risk:MaxCorrelatedExposure", 0.5m);
            var totalExposure = symbolExposure.Values.Sum();

            if (totalExposure > 0)
            {
                var currentSymbolExposure = symbolExposure.GetValueOrDefault(signal.UnderlyingSymbol, 0);
                var proposedExposure = currentSymbolExposure + signal.MaxLoss;

                if (proposedExposure / totalExposure > maxCorrelatedExposure)
                {
                    _logger.LogWarning($"Correlation risk too high for {signal.UnderlyingSymbol}: {proposedExposure / totalExposure:P}");
                    return false;
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking correlation risk");
            return false;
        }
    }

    public async Task<decimal> CalculateDynamicPositionSizeAsync(TradingSignal signal, MarketRegime regime)
    {
        try
        {
            var basePositionSize = await CalculatePositionSizeAsync(signal);
            var riskAdjustment = await GetRiskAdjustmentAsync(regime);

            return Math.Max(1, Math.Floor(basePositionSize * riskAdjustment.PositionSizeMultiplier));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating dynamic position size");
            return 1;
        }
    }

    public async Task<bool> CheckDrawdownLimitsAsync()
    {
        try
        {
            var account = await _alpacaService.GetAccountAsync();
            if (account == null) return false;

            var maxDrawdown = _configuration.GetValue<decimal>("Risk:MaxDrawdown", 0.1m);
            var equity = account.Equity ?? 0;
            var dayTradeEquity = account.DayTradingBuyingPower ?? equity;

            // Simple drawdown check (would need historical high water mark in production)
            var currentDrawdown = Math.Max(0, (dayTradeEquity - equity) / dayTradeEquity);

            if (currentDrawdown > maxDrawdown)
            {
                _logger.LogWarning($"Drawdown limit exceeded: {currentDrawdown:P} > {maxDrawdown:P}");
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking drawdown limits");
            return false;
        }
    }

    public async Task<decimal> GetVaRLimitAsync()
    {
        try
        {
            var account = await _alpacaService.GetAccountAsync();
            var varLimit = _configuration.GetValue<decimal>("Risk:VaRLimit", 0.05m);
            return (account?.Equity ?? 0) * varLimit;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting VaR limit");
            return 0;
        }
    }

    public async Task<bool> ValidatePortfolioConcentrationAsync(TradingSignal signal)
    {
        try
        {
            var metrics = await GetPortfolioRiskMetricsAsync();
            var maxConcentration = _configuration.GetValue<decimal>("Risk:MaxConcentration", 0.4m);

            // Check if adding this signal would exceed concentration limits
            var currentExposure = metrics.SymbolExposure.GetValueOrDefault(signal.UnderlyingSymbol, 0);
            var proposedExposure = currentExposure + signal.MaxLoss;
            var totalExposure = metrics.TotalExposure + signal.MaxLoss;

            if (totalExposure > 0 && proposedExposure / totalExposure > maxConcentration)
            {
                _logger.LogWarning($"Concentration limit exceeded for {signal.UnderlyingSymbol}: {proposedExposure / totalExposure:P}");
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating portfolio concentration");
            return false;
        }
    }

    public async Task<RiskAdjustment> GetRiskAdjustmentAsync(MarketRegime regime)
    {
        try
        {
            var adjustment = new RiskAdjustment();
            var account = await _alpacaService.GetAccountAsync();

            // Adjust based on market regime
            switch (regime.VolatilityRegime)
            {
                case VolatilityRegime.Low:
                    adjustment.PositionSizeMultiplier = 1.2m;
                    adjustment.ConfidenceThreshold = 0.55m;
                    adjustment.Reason = "Low volatility - increased position sizing";
                    break;

                case VolatilityRegime.High:
                    adjustment.PositionSizeMultiplier = 0.5m;
                    adjustment.ConfidenceThreshold = 0.75m;
                    adjustment.AllowNewPositions = false;
                    adjustment.Reason = "High volatility - reduced risk";
                    break;

                default:
                    adjustment.PositionSizeMultiplier = 1.0m;
                    adjustment.ConfidenceThreshold = 0.6m;
                    adjustment.Reason = "Normal market conditions";
                    break;
            }

            // Adjust based on trend
            if (regime.Trend == MarketTrend.Bearish)
            {
                adjustment.PositionSizeMultiplier *= 0.7m;
                adjustment.ConfidenceThreshold += 0.1m;
                adjustment.Reason += " + Bearish trend adjustment";
            }

            adjustment.MaxRiskPerTrade = (account?.Equity ?? 0) *
                _configuration.GetValue<decimal>("Trading:RiskPerTrade", 0.01m) *
                adjustment.PositionSizeMultiplier;

            return adjustment;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting risk adjustment");
            return new RiskAdjustment();
        }
    }

    // Helper methods for risk calculations
    private decimal CalculateNetDelta(IEnumerable<Alpaca.Markets.IPosition> positions)
    {
        // Simplified delta calculation - in production, would use real option Greeks
        decimal netDelta = 0;
        foreach (var position in positions)
        {
            if (IsOptionSymbol(position.Symbol))
            {
                // Estimate delta based on option type and moneyness
                var estimatedDelta = EstimateOptionDelta(position.Symbol);
                netDelta += estimatedDelta * position.Quantity;
            }
            else
            {
                // Stock positions have delta of 1
                netDelta += position.Quantity;
            }
        }
        return netDelta;
    }

    private decimal CalculateNetGamma(IEnumerable<Alpaca.Markets.IPosition> positions)
    {
        // Simplified gamma calculation
        decimal netGamma = 0;
        foreach (var position in positions.Where(p => IsOptionSymbol(p.Symbol)))
        {
            var estimatedGamma = EstimateOptionGamma(position.Symbol);
            netGamma += estimatedGamma * position.Quantity;
        }
        return netGamma;
    }

    private decimal CalculateNetTheta(IEnumerable<Alpaca.Markets.IPosition> positions)
    {
        // Simplified theta calculation
        decimal netTheta = 0;
        foreach (var position in positions.Where(p => IsOptionSymbol(p.Symbol)))
        {
            var estimatedTheta = EstimateOptionTheta(position.Symbol);
            netTheta += estimatedTheta * position.Quantity;
        }
        return netTheta;
    }

    private decimal CalculateNetVega(IEnumerable<Alpaca.Markets.IPosition> positions)
    {
        // Simplified vega calculation
        decimal netVega = 0;
        foreach (var position in positions.Where(p => IsOptionSymbol(p.Symbol)))
        {
            var estimatedVega = EstimateOptionVega(position.Symbol);
            netVega += estimatedVega * position.Quantity;
        }
        return netVega;
    }

    private decimal CalculatePortfolioVaR(IEnumerable<Alpaca.Markets.IPosition> positions)
    {
        // Simplified VaR calculation - 95% confidence level
        var totalValue = positions.Sum(p => Math.Abs(p.MarketValue ?? 0));
        var estimatedVolatility = 0.15m; // 15% annual volatility assumption
        var dailyVolatility = estimatedVolatility / (decimal)Math.Sqrt(252);
        return totalValue * dailyVolatility * 1.645m; // 95% confidence
    }

    private bool IsOptionSymbol(string symbol)
    {
        // Simple check for option symbols (contains expiration date pattern)
        return symbol.Length > 10 && char.IsDigit(symbol[^8]);
    }

    private string ExtractUnderlyingSymbol(string optionSymbol)
    {
        if (!IsOptionSymbol(optionSymbol))
            return optionSymbol;

        // Extract underlying from option symbol (simplified)
        var parts = optionSymbol.Split('_');
        return parts.Length > 0 ? parts[0] : optionSymbol.Substring(0, Math.Min(3, optionSymbol.Length));
    }

    private decimal EstimateOptionDelta(string optionSymbol)
    {
        // Simplified delta estimation - in production, use real option pricing models
        if (optionSymbol.Contains('C')) // Call option
            return 0.5m; // Assume ATM
        else // Put option
            return -0.5m; // Assume ATM
    }

    private decimal EstimateOptionGamma(string optionSymbol)
    {
        // Simplified gamma estimation
        return 0.02m; // Assume moderate gamma
    }

    private decimal EstimateOptionTheta(string optionSymbol)
    {
        // Simplified theta estimation - options lose value over time
        return -0.05m; // Assume moderate time decay
    }

    private decimal EstimateOptionVega(string optionSymbol)
    {
        // Simplified vega estimation
        return 0.1m; // Assume moderate volatility sensitivity
    }
}
